<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhangzc.bookauth.Mapper.TRoleMapper">

    <resultMap id="BaseResultMap" type="com.zhangzc.bookauth.Pojo.domain.TRole">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="roleName" column="role_name" jdbcType="VARCHAR"/>
            <result property="roleKey" column="role_key" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,role_name,role_key,
        status,sort,remark,
        create_time,update_time,is_deleted
    </sql>
</mapper>
