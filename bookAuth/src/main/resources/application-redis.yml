spring:
  datasource:
  data:
    redis:
      database: 2 # Redis 数据库索引（默认为 0）
      host: ************  # Redis 服务器地址
      port: 6379 # Redis 服务器连接端口
      # Redis 服务器连接密码（默认为空）
      timeout: 5s # 读超时时间
      connect-timeout: 5s # 链接超时时间
      lettuce:
        pool:
          max-active: 200 # 连接池最大连接数
          max-wait: -1ms # 连接池最大阻塞等待时间（使用负值表示没有限制）
          min-idle: 0 # 连接池中的最小空闲连接
          max-idle: 10 # 连接池中的最大空闲连接
