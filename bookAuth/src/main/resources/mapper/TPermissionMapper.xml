<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhangzc.bookauth.Mapper.TPermissionMapper">

    <resultMap id="BaseResultMap" type="com.zhangzc.bookauth.Pojo.domain.TPermission">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="menuUrl" column="menu_url" jdbcType="VARCHAR"/>
            <result property="menuIcon" column="menu_icon" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="permissionKey" column="permission_key" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,name,
        type,menu_url,menu_icon,
        sort,permission_key,status,
        create_time,update_time,is_deleted
    </sql>
</mapper>
