<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhangzc.bookauth.Mapper.TUserMapper">

    <resultMap id="BaseResultMap" type="com.zhangzc.bookauth.Pojo.domain.TUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="username" column="username" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,username,create_time,
        update_time
    </sql>
</mapper>
