<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhangzc.bookauth.Mapper.TUserRoleRelMapper">

    <resultMap id="BaseResultMap" type="com.zhangzc.bookauth.Pojo.domain.TUserRoleRel">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="roleId" column="role_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,role_id,
        create_time,update_time,is_deleted
    </sql>
</mapper>
