package com.zhangzc.bookauth.Service.impl;

import com.zhangzc.bookauth.Const.RedisKeyConstants;
import com.zhangzc.bookauth.Enum.LoginTypeEnum;
import com.zhangzc.bookauth.Pojo.Vo.UserLoginReqVO;
import com.zhangzc.bookauth.Pojo.domain.TUser;
import com.zhangzc.bookauth.Service.TUserService;
import com.zhangzc.bookauth.Service.UserService;
import com.zhangzc.bookauth.Utils.RedisUtil;
import com.zhangzc.bookcommon.Exceptions.BizException;
import com.zhangzc.bookcommon.Exceptions.ExceptionEnum;
import com.zhangzc.bookcommon.Utils.R;
import com.zhangzc.fakebookspringbootstartjackon.Utils.JsonUtils;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final TUserService userService;
    private final RedisUtil redisUtil;


    @Override
    public R loginAndRegister(UserLoginReqVO userLoginReqVO) throws BizException {
        String phone = userLoginReqVO.getPhone();
        Integer type = userLoginReqVO.getType();

        LoginTypeEnum loginTypeEnum = LoginTypeEnum.valueOf(type);

        Long userId = null;

        // 判断登录类型
        switch (loginTypeEnum) {
            case VERIFICATION_CODE: // 验证码登录
                String verificationCode = userLoginReqVO.getCode();

                // 校验入参验证码是否为空
                if (StringUtils.isBlank(verificationCode)) {
                    return R.fail(String.valueOf(500), "验证码不能为空");
                }

                // 构建验证码 Redis Key
                String key = RedisKeyConstants.buildVerificationCodeKey(phone);
                // 查询存储在 Redis 中该用户的登录验证码
                String sentCode = (String) redisUtil.get(key);

                // 判断用户提交的验证码，与 Redis 中的验证码是否一致
                if (StringUtils.equals(verificationCode, sentCode)) {
                    throw new BizException(ExceptionEnum.VERIFICATION_CODE_ERROR);
                }

                // 通过手机号查询记录
                TUser userDO = tuserService.lam(phone);

                log.info("==> 用户是否注册, phone: {}, userDO: {}", phone, JsonUtils.toJsonString(userDO));

                // 判断是否注册
                if (Objects.isNull(userDO)) {
                    // 若此用户还没有注册，系统自动注册该用户
                    // todo

                } else {
                    // 已注册，则获取其用户 ID
                    userId = userDO.getId();
                }
                break;
            case PASSWORD: // 密码登录
                // todo

                break;
            default:
                break;
        }

        // SaToken 登录用户，并返回 token 令牌
        // todo

        return Response.success("");
    }

    }
}
