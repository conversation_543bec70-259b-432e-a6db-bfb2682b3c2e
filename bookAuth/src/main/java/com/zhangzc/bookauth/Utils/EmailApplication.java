package com.zhangzc.bookauth.Utils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class EmailApplication implements CommandLineRunner {
    @Autowired
    private EmailService emailService;
    public static void main(String[] args) {
        SpringApplication.run(EmailApplication.class, args);
    }
    @Override
    public void run(String... args) throws Exception {
        // 发送邮件示例
        emailService.sendSimpleMessage("<EMAIL>", "测试邮件", "这是一封测试邮件！");
    }
}