<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhangzc.bookauth.Mapper.TUserMapper">

    <resultMap id="BaseResultMap" type="com.zhangzc.bookauth.Pojo.domain.TUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="xiaohashuId" column="xiaohashu_id" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="birthday" column="birthday" jdbcType="DATE"/>
            <result property="backgroundImg" column="background_img" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="TINYINT"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="introduction" column="introduction" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDeleted" column="is_deleted" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,xiaohashu_id,password,
        nickname,avatar,birthday,
        background_img,phone,sex,
        status,introduction,create_time,
        update_time,is_deleted
    </sql>
</mapper>
